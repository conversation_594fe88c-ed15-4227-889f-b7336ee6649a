#!/usr/bin/env python3
"""
标签爬取脚本 - 专门用于爬取所有标签
"""
import sys
from loguru import logger
from utils.db_utils import reset_database, get_table_counts
from shangshiwen_spider import ShangShiWenSpider

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )

def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 50)
    logger.info("🏷️  启动标签爬取器")
    logger.info("=" * 50)
    
    # 重置数据库
    logger.info("🔄 重置数据库...")
    reset_database()
    
    # 显示初始状态
    logger.info("📊 初始数据库状态:")
    counts = get_table_counts()
    for table, count in counts.items():
        if count > 0:
            logger.info(f"  {table}: {count} 条记录")
    
    # 开始爬取标签
    logger.info("\n🚀 开始爬取标签...")
    spider = ShangShiWenSpider()
    tags_data = spider.crawl_all_tags()
    
    if tags_data:
        logger.success(f"🎉 标签爬取成功！")
        
        # 显示统计信息
        logger.info("\n📈 爬取统计:")
        logger.info(f"  总标签数: {len(tags_data)}")
        
        # 按诗词数量排序，显示前10个
        sorted_tags = sorted(tags_data, key=lambda x: x['count'], reverse=True)
        logger.info("\n🔥 热门标签 TOP 10:")
        for i, tag in enumerate(sorted_tags[:10], 1):
            logger.info(f"  {i:2d}. {tag['name']} ({tag['count']:,} 首)")
        
        # 显示最终数据库状态
        logger.info("\n📊 最终数据库状态:")
        final_counts = get_table_counts()
        for table, count in final_counts.items():
            if count > 0:
                logger.info(f"  {table}: {count} 条记录")
        
        logger.success("✅ 标签爬取完成！")
        
    else:
        logger.error("❌ 标签爬取失败！")

if __name__ == "__main__":
    main()
