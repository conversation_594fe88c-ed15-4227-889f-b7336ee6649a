"""
数据库模型定义 - 适配 shangshiwen.com
"""
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean, ForeignKey, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
from config import DATABASE_URL

Base = declarative_base()


class Dynasty(Base):
    """朝代模型"""
    __tablename__ = 'shiwen_dynasties'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(50), nullable=False, unique=True, comment='朝代名称')
    description = Column(Text, comment='朝代描述')
    sort_order = Column(Integer, default=0, comment='排序字段')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

    # 关系
    poems = relationship("Poem", back_populates="dynasty")
    authors = relationship("Author", back_populates="dynasty")


class Author(Base):
    """作者模型"""
    __tablename__ = 'shiwen_authors'

    id = Column(Integer, primary_key=True, autoincrement=True)
    author_id = Column(Integer, unique=True, comment='网站作者ID')
    name = Column(String(100), nullable=False, comment='作者姓名')
    dynasty_id = Column(Integer, ForeignKey(
        'shiwen_dynasties.id'), comment='朝代ID')
    birth_year = Column(String(20), comment='出生年份')
    death_year = Column(String(20), comment='逝世年份')
    biography = Column(Text, comment='生平简介')
    image_url = Column(String(500), comment='头像图片URL')
    image_filename = Column(String(200), comment='本地保存的图片文件名')
    source_url = Column(String(500), comment='来源URL')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now,
                        onupdate=datetime.now, comment='更新时间')

    # 关系
    dynasty = relationship("Dynasty", back_populates="authors")
    poems = relationship("Poem", back_populates="author")


class PoemTag(Base):
    """诗词标签模型（原标签表）"""
    __tablename__ = 'shiwen_poem_tags'

    id = Column(Integer, primary_key=True, autoincrement=True)
    tag_id = Column(Integer, unique=True, comment='网站标签ID')
    name = Column(String(100), nullable=False, comment='标签名称')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')


class Poem(Base):
    """古诗词模型"""
    __tablename__ = 'shiwen_poems'

    id = Column(Integer, primary_key=True, autoincrement=True)
    poem_id = Column(Integer, unique=True, nullable=False, comment='网站诗词ID')
    title = Column(String(200), nullable=False, comment='诗词标题')
    author_id = Column(Integer, ForeignKey('shiwen_authors.id'),
                       nullable=False, comment='作者ID')
    dynasty_id = Column(Integer, ForeignKey(
        'shiwen_dynasties.id'), comment='朝代ID')
    content = Column(Text, nullable=False, comment='诗词内容(HTML格式)')
    source_url = Column(String(500), comment='来源URL')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now,
                        onupdate=datetime.now, comment='更新时间')
    is_active = Column(Boolean, default=True, comment='是否有效')

    # 关系
    author = relationship("Author", back_populates="poems")
    dynasty = relationship("Dynasty", back_populates="poems")
    translations = relationship("Translation", back_populates="poem")
    appreciations = relationship("Appreciation", back_populates="poem")


# 删除了原来的 PoemTag 关联表模型


class Translation(Base):
    """翻译模型"""
    __tablename__ = 'shiwen_translations'

    id = Column(Integer, primary_key=True, autoincrement=True)
    poem_id = Column(Integer, ForeignKey('shiwen_poems.id'),
                     nullable=False, comment='诗词ID')
    title = Column(String(200), comment='翻译标题')
    content = Column(Text, comment='翻译内容')
    source_url = Column(String(500), comment='来源URL')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now,
                        onupdate=datetime.now, comment='更新时间')

    # 关系
    poem = relationship("Poem", back_populates="translations")


class Appreciation(Base):
    """赏析模型"""
    __tablename__ = 'shiwen_appreciations'

    id = Column(Integer, primary_key=True, autoincrement=True)
    poem_id = Column(Integer, ForeignKey('shiwen_poems.id'),
                     nullable=False, comment='诗词ID')
    title = Column(String(200), comment='赏析标题')
    content = Column(Text, comment='赏析内容')
    source_url = Column(String(500), comment='来源URL')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now,
                        onupdate=datetime.now, comment='更新时间')

    # 关系
    poem = relationship("Poem", back_populates="appreciations")


# 创建数据库引擎
engine = create_engine(DATABASE_URL, echo=False)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def create_tables():
    """创建所有表"""
    Base.metadata.create_all(bind=engine)
    print("数据库表创建成功！")


def init_dynasties():
    """初始化朝代数据"""
    dynasties = [
        ('先秦', '先秦时期', 1),
        ('两汉', '西汉和东汉', 2),
        ('魏晋', '魏晋时期', 3),
        ('南北朝', '南北朝时期', 4),
        ('隋代', '隋朝', 5),
        ('唐朝', '唐朝', 6),
        ('五代', '五代十国', 7),
        ('宋代', '北宋和南宋', 8),
        ('金朝', '金朝', 9),
        ('元代', '元朝', 10),
        ('明代', '明朝', 11),
        ('清代', '清朝', 12),
    ]

    db = SessionLocal()
    try:
        for name, desc, order in dynasties:
            existing = db.query(Dynasty).filter(Dynasty.name == name).first()
            if not existing:
                dynasty = Dynasty(
                    name=name, description=desc, sort_order=order)
                db.add(dynasty)
        db.commit()
        print("朝代数据初始化成功！")
    except Exception as e:
        db.rollback()
        print(f"朝代数据初始化失败: {e}")
    finally:
        db.close()


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


if __name__ == "__main__":
    create_tables()
    init_dynasties()
