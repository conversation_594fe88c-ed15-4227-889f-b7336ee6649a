#!/usr/bin/env python3
"""
测试诗人爬虫脚本
"""
import sys
from loguru import logger
from utils.db_utils import reset_database, get_table_counts
from author_spider import AuthorSpider


def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )


def test_author_spider():
    """测试诗人爬虫"""
    logger.info("=" * 50)
    logger.info("🕷️  启动诗人爬虫测试")
    logger.info("=" * 50)
    
    # 重置数据库
    logger.info("🔄 重置数据库...")
    reset_database()
    
    # 显示初始状态
    logger.info("📊 初始数据库状态:")
    counts = get_table_counts()
    for table, count in counts.items():
        if count > 0:
            logger.info(f"  {table}: {count} 条记录")
    
    # 开始爬取第一个诗人
    logger.info("\n🚀 开始爬取第一个诗人...")
    spider = AuthorSpider()
    result = spider.crawl_first_author()
    
    if result:
        logger.success(f"🎉 诗人爬取成功！")
        
        # 显示爬取结果
        logger.info("\n📈 爬取结果:")
        logger.info(f"  作者ID: {result['author_id']}")
        logger.info(f"  作者名: {result['name']}")
        logger.info(f"  朝代: {result['dynasty_name']} (ID: {result['dynasty_id']})")
        logger.info(f"  图片文件: {result['image_filename'] or '无'}")
        logger.info(f"  简介长度: {len(result['biography'])} 字符")
        
        # 显示最终数据库状态
        logger.info("\n📊 最终数据库状态:")
        final_counts = get_table_counts()
        for table, count in final_counts.items():
            if count > 0:
                logger.info(f"  {table}: {count} 条记录")
        
        logger.success("✅ 诗人爬取测试完成！")
        
    else:
        logger.error("❌ 诗人爬取失败！")


def main():
    """主函数"""
    setup_logging()
    test_author_spider()


if __name__ == "__main__":
    main()
