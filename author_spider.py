#!/usr/bin/env python3
"""
诗人爬取脚本 - 专门用于爬取诗人信息
"""
import os
import re
import requests
from bs4 import BeautifulSoup
from loguru import logger
from models import SessionLocal, Author, Dynasty
from urllib.parse import urljoin, urlparse


class AuthorSpider:
    """诗人爬虫类"""

    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        self.base_url = "http://shangshiwen.com"

        # 朝代名称到ID的映射（基于数据库中的数据）
        self.dynasty_mapping = {
            '先秦': 1,
            '两汉': 2,
            '魏晋': 3,
            '南北朝': 4,
            '隋代': 5,
            '唐朝': 6,
            '唐代': 6,  # 网站可能使用"唐代"
            '五代': 7,
            '宋代': 8,
            '金朝': 9,
            '元代': 10,
            '明代': 11,
            '清代': 12,
            '近现代': 13,
        }

        # 创建图片保存目录
        self.image_dir = "author_images"
        if not os.path.exists(self.image_dir):
            os.makedirs(self.image_dir)
            logger.info(f"创建图片保存目录: {self.image_dir}")

    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)

    def parse_author_list_page(self, url):
        """解析诗人列表页面"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            response.encoding = 'utf-8'
            logger.info(f"成功获取诗人列表页面: {url}")

            soup = BeautifulSoup(response.text, 'lxml')

            # 查找所有诗人条目
            author_items = soup.find_all('div', class_='gushiList')
            logger.info(f"找到 {len(author_items)} 个诗人条目")

            authors = []
            for item in author_items:
                author_info = self.parse_author_item(item)
                if author_info:
                    authors.append(author_info)

            return authors

        except Exception as e:
            logger.error(f"解析诗人列表页面失败: {e}")
            return []

    def parse_author_item(self, item):
        """解析单个诗人条目"""
        try:
            # 提取诗人名称和详情页面URL - 查找包含<b>标签的链接
            name_link = None
            name = ""
            detail_url = ""

            # 查找所有链接，找到包含<b>标签的那个
            links = item.find_all('a')
            for link in links:
                b_tag = link.find('b')
                if b_tag:
                    name = b_tag.get_text().strip()
                    detail_url = link.get('href')
                    name_link = link
                    break

            if not name_link or not name:
                logger.warning("未找到诗人名称链接")
                return None

            # 提取朝代信息
            dynasty_text = ""
            dynasty_p = item.find(
                'p', style=lambda x: x and 'color:#676767' in x)
            if dynasty_p:
                dynasty_text = dynasty_p.get_text().strip()
                # 去除"朝代："前缀
                if '朝代：' in dynasty_text:
                    dynasty_text = dynasty_text.replace('朝代：', '').strip()

            logger.info(f"解析诗人: {name}, 朝代: {dynasty_text}, URL: {detail_url}")

            return {
                'name': name,
                'detail_url': detail_url,
                'dynasty_name': dynasty_text
            }

        except Exception as e:
            logger.error(f"解析诗人条目失败: {e}")
            return None

    def parse_author_detail_page(self, author_info):
        """解析诗人详情页面"""
        try:
            detail_url = urljoin(self.base_url, author_info['detail_url'])
            response = self.session.get(detail_url)
            response.raise_for_status()
            response.encoding = 'utf-8'
            logger.info(f"成功获取诗人详情页面: {detail_url}")

            soup = BeautifulSoup(response.text, 'lxml')

            # 提取作者ID（从URL中提取）
            author_id = self.extract_author_id(author_info['detail_url'])

            # 提取作者图片
            image_url = None
            image_filename = None
            content_div = soup.find('div', class_='content')
            if content_div:
                img_tag = content_div.find('img')
                if img_tag:
                    img_src = img_tag.get('src')
                    if img_src and '/authorImg/' in img_src:
                        image_url = urljoin(self.base_url, img_src)
                        image_filename = os.path.basename(img_src)
                        logger.info(f"找到作者图片: {image_url}")

            # 提取作者简介
            biography = ""
            bio_div = soup.find(
                'div', style=lambda x: x and 'text-indent:2em' in x)
            if bio_div:
                biography = bio_div.get_text().strip()
                logger.info(f"提取简介长度: {len(biography)} 字符")

            # 获取朝代ID
            dynasty_id = self.dynasty_mapping.get(author_info['dynasty_name'])
            if not dynasty_id:
                logger.warning(f"未找到朝代 '{author_info['dynasty_name']}' 的ID映射")

            return {
                'author_id': author_id,
                'name': author_info['name'],
                'dynasty_name': author_info['dynasty_name'],
                'dynasty_id': dynasty_id,
                'biography': biography,
                'image_url': image_url,
                'image_filename': image_filename,
                'source_url': detail_url
            }

        except Exception as e:
            logger.error(f"解析诗人详情页面失败: {e}")
            return None

    def extract_author_id(self, url):
        """从URL中提取作者ID"""
        try:
            # URL格式: /author_10011.html
            match = re.search(r'/author_(\d+)\.html', url)
            if match:
                return int(match.group(1))
            return None
        except Exception as e:
            logger.error(f"提取作者ID失败: {e}")
            return None

    def download_image(self, image_url, filename):
        """下载并保存图片"""
        try:
            if not image_url or not filename:
                return False

            response = self.session.get(image_url)
            response.raise_for_status()

            filepath = os.path.join(self.image_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(response.content)

            logger.success(f"图片下载成功: {filepath}")
            return True

        except Exception as e:
            logger.error(f"下载图片失败: {e}")
            return False

    def save_author_to_db(self, author_data):
        """保存作者信息到数据库"""
        if not author_data or not author_data.get('author_id'):
            logger.warning("作者数据为空或缺少作者ID，跳过保存")
            return False

        db = SessionLocal()
        try:
            # 检查是否已存在
            existing = db.query(Author).filter(
                Author.author_id == author_data['author_id']
            ).first()

            if existing:
                logger.info(f"作者已存在，更新信息: {author_data['name']}")
                # 更新现有记录
                existing.name = author_data['name']
                existing.dynasty_id = author_data['dynasty_id']
                existing.dynasty_name = author_data['dynasty_name']
                existing.biography = author_data['biography']
                existing.image_url = author_data['image_url']
                existing.image_filename = author_data['image_filename']
                existing.source_url = author_data['source_url']
            else:
                logger.info(f"创建新作者记录: {author_data['name']}")
                # 创建新记录
                author = Author(
                    author_id=author_data['author_id'],
                    name=author_data['name'],
                    dynasty_id=author_data['dynasty_id'],
                    dynasty_name=author_data['dynasty_name'],
                    biography=author_data['biography'],
                    image_url=author_data['image_url'],
                    image_filename=author_data['image_filename'],
                    source_url=author_data['source_url']
                )
                db.add(author)

            db.commit()
            logger.success(f"✅ 成功保存作者: {author_data['name']}")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"✗ 保存作者到数据库失败: {e}")
            return False
        finally:
            db.close()

    def crawl_first_author(self, list_url="http://shangshiwen.com/shiren_1_0.html"):
        """爬取第一个诗人的完整信息"""
        logger.info(f"🕷️  开始爬取第一个诗人信息")

        # 1. 解析列表页面
        authors = self.parse_author_list_page(list_url)
        if not authors:
            logger.error("未找到任何诗人信息")
            return None

        # 2. 获取第一个诗人
        first_author = authors[0]
        logger.info(f"选择第一个诗人: {first_author['name']}")

        # 3. 解析详情页面
        author_detail = self.parse_author_detail_page(first_author)
        if not author_detail:
            logger.error("解析诗人详情失败")
            return None

        # 4. 下载图片
        if author_detail['image_url'] and author_detail['image_filename']:
            self.download_image(
                author_detail['image_url'], author_detail['image_filename'])

        # 5. 保存到数据库
        success = self.save_author_to_db(author_detail)
        if success:
            logger.success(f"🎉 成功爬取并保存诗人: {author_detail['name']}")
            return author_detail
        else:
            logger.error(f"❌ 保存诗人失败: {author_detail['name']}")
            return None


def main():
    """测试函数"""
    spider = AuthorSpider()

    # 爬取第一个诗人
    result = spider.crawl_first_author()

    if result:
        print("\n" + "="*60)
        print("🎯 诗人爬取结果")
        print("="*60)
        print(f"📖 作者ID: {result['author_id']}")
        print(f"👤 作者名: {result['name']}")
        print(f"🏛️  朝代: {result['dynasty_name']} (ID: {result['dynasty_id']})")
        print(f"🖼️  图片: {result['image_filename'] or '无'}")
        print(f"📝 简介长度: {len(result['biography'])} 字符")
        print(f"🔗 来源: {result['source_url']}")

        if result['biography']:
            print(f"\n📖 简介内容:")
            print(result['biography'][:200] +
                  "..." if len(result['biography']) > 200 else result['biography'])

        print("="*60)


if __name__ == "__main__":
    main()
